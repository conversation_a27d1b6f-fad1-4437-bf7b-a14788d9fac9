"""
Enhanced main window for CMOST application with complete workflow.

This module implements an enhanced main window that provides a complete
workflow for CMOST simulation including calibration, screening configuration,
and output management.
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import json
from typing import Dict, Any, Optional
from pathlib import Path

from ..config.settings import Settings, get_setting, set_setting
try:
    from ..calibration.integrated_calibration import IntegratedCalibrator, CalibrationMethod
except ImportError:
    # Fallback if calibration module is not available
    IntegratedCalibrator = None
    CalibrationMethod = None

try:
    from ..screening.strategy_manager import ScreeningStrategyManager
except ImportError:
    # Fallback if screening module is not available
    ScreeningStrategyManager = None

# Import UI panels - these should be available since we just created them
try:
    from .calibration_panel import CalibrationPanel
except ImportError:
    CalibrationPanel = None

try:
    from .screening_config_panel import ScreeningConfigPanel
except ImportError:
    ScreeningConfigPanel = None

try:
    from .output_config_panel import OutputConfigPanel
except ImportError:
    OutputConfigPanel = None


class EnhancedMainWindow:
    """
    Enhanced main window for CMOST application.
    
    Provides a complete workflow interface including:
    1. File selection and basic configuration
    2. Calibration functionality
    3. Screening strategy configuration
    4. Output configuration
    5. Simulation execution and results
    """
    
    def __init__(self, root):
        """
        Initialize the enhanced main window.
        
        Args:
            root: Tkinter root window
        """
        self.root = root
        self.root.title("CMOST - 结直肠癌筛查仿真工具")
        self.root.geometry("1200x800")
        
        # Application state
        self.settings = Settings()
        self.current_step = 0
        self.workflow_steps = [
            "文件选择", "调参配置", "筛查配置", "输出配置", "模拟执行"
        ]
        
        # Data storage
        self.setting_file_path = None
        self.benchmark_file_path = None
        self.calibration_results = None
        self.screening_config = {}
        self.output_config = {}

        # Simulation control variables
        self.simulation_running = False
        self.simulation_paused = False
        self.simulation_stopped = False
        self.simulation_thread = None
        
        # UI components
        self.create_ui()
        
        # Initialize workflow
        self.show_step(0)
    
    def create_ui(self):
        """Create the user interface components."""
        # Main container
        self.main_frame = ttk.Frame(self.root, padding="10")
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create workflow header
        self.create_workflow_header()
        
        # Create content area
        self.content_frame = ttk.Frame(self.main_frame)
        self.content_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # Create navigation buttons
        self.create_navigation_buttons()
        
        # Create status bar
        self.create_status_bar()
    
    def create_workflow_header(self):
        """Create the workflow progress header."""
        header_frame = ttk.Frame(self.main_frame)
        header_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Title
        title_label = ttk.Label(
            header_frame, 
            text="CMOST 结直肠癌筛查仿真工具", 
            font=("Arial", 16, "bold")
        )
        title_label.pack(pady=(0, 10))
        
        # Workflow progress
        progress_frame = ttk.Frame(header_frame)
        progress_frame.pack(fill=tk.X)
        
        self.step_labels = []
        for i, step_name in enumerate(self.workflow_steps):
            # Step number
            step_num_frame = ttk.Frame(progress_frame)
            step_num_frame.pack(side=tk.LEFT, padx=5)
            
            step_num_label = ttk.Label(
                step_num_frame, 
                text=str(i + 1), 
                font=("Arial", 12, "bold"),
                width=3,
                anchor=tk.CENTER,
                relief=tk.RAISED,
                borderwidth=2
            )
            step_num_label.pack()
            
            # Step name
            step_name_label = ttk.Label(
                step_num_frame, 
                text=step_name, 
                font=("Arial", 10)
            )
            step_name_label.pack()
            
            self.step_labels.append((step_num_label, step_name_label))
            
            # Arrow (except for last step)
            if i < len(self.workflow_steps) - 1:
                arrow_label = ttk.Label(progress_frame, text="→", font=("Arial", 14))
                arrow_label.pack(side=tk.LEFT, padx=5)
    
    def create_navigation_buttons(self):
        """Create navigation buttons."""
        nav_frame = ttk.Frame(self.main_frame)
        nav_frame.pack(fill=tk.X, pady=10)
        
        # Previous button
        self.prev_button = ttk.Button(
            nav_frame, 
            text="← 上一步", 
            command=self.previous_step,
            state=tk.DISABLED
        )
        self.prev_button.pack(side=tk.LEFT, padx=5)
        
        # Next button
        self.next_button = ttk.Button(
            nav_frame, 
            text="下一步 →", 
            command=self.next_step
        )
        self.next_button.pack(side=tk.LEFT, padx=5)
        
        # Skip calibration button (only visible in step 1)
        self.skip_calibration_button = ttk.Button(
            nav_frame, 
            text="跳过调参", 
            command=self.skip_calibration
        )
        
        # Execute simulation button (only visible in last step)
        self.execute_button = ttk.Button(
            nav_frame, 
            text="开始模拟", 
            command=self.execute_simulation
        )
        
        # Reset button
        self.reset_button = ttk.Button(
            nav_frame, 
            text="重置", 
            command=self.reset_workflow
        )
        self.reset_button.pack(side=tk.RIGHT, padx=5)
    
    def create_status_bar(self):
        """Create status bar."""
        self.status_bar = ttk.Frame(self.main_frame)
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM)
        
        self.status_label = ttk.Label(
            self.status_bar, 
            text="就绪", 
            relief=tk.SUNKEN, 
            anchor=tk.W
        )
        self.status_label.pack(fill=tk.X, padx=2, pady=2)
    
    def show_step(self, step_index: int):
        """
        Show the specified workflow step.
        
        Args:
            step_index: Index of the step to show
        """
        if step_index < 0 or step_index >= len(self.workflow_steps):
            return
        
        self.current_step = step_index
        
        # Update step highlighting
        self.update_step_highlighting()
        
        # Clear content frame
        for widget in self.content_frame.winfo_children():
            widget.destroy()
        
        # Show appropriate content
        if step_index == 0:
            self.show_file_selection_step()
        elif step_index == 1:
            self.show_calibration_step()
        elif step_index == 2:
            self.show_screening_config_step()
        elif step_index == 3:
            self.show_output_config_step()
        elif step_index == 4:
            self.show_simulation_step()
        
        # Update navigation buttons
        self.update_navigation_buttons()
        
        # Update status
        self.set_status(f"当前步骤: {self.workflow_steps[step_index]}")
    
    def update_step_highlighting(self):
        """Update the highlighting of workflow steps."""
        for i, (num_label, name_label) in enumerate(self.step_labels):
            if i == self.current_step:
                # Current step - highlight
                num_label.configure(background="#4CAF50", foreground="white")
                name_label.configure(font=("Arial", 10, "bold"))
            elif i < self.current_step:
                # Completed step - green
                num_label.configure(background="#81C784", foreground="white")
                name_label.configure(font=("Arial", 10))
            else:
                # Future step - default
                num_label.configure(background="SystemButtonFace", foreground="black")
                name_label.configure(font=("Arial", 10))
    
    def update_navigation_buttons(self):
        """Update the state of navigation buttons."""
        # Previous button
        self.prev_button.configure(state=tk.NORMAL if self.current_step > 0 else tk.DISABLED)
        
        # Next button
        if self.current_step < len(self.workflow_steps) - 1:
            self.next_button.pack(side=tk.LEFT, padx=5)
            self.execute_button.pack_forget()
        else:
            self.next_button.pack_forget()
            self.execute_button.pack(side=tk.LEFT, padx=5)
        
        # Skip calibration button (only in step 1)
        if self.current_step == 1:
            self.skip_calibration_button.pack(side=tk.LEFT, padx=5)
        else:
            self.skip_calibration_button.pack_forget()
    
    def show_file_selection_step(self):
        """Show file selection and basic configuration step."""
        # Create file selection panel
        file_frame = ttk.LabelFrame(self.content_frame, text="文件选择", padding=10)
        file_frame.pack(fill=tk.X, pady=5)
        
        # Setting file selection
        ttk.Label(file_frame, text="Setting文件:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.setting_file_var = tk.StringVar()
        setting_entry = ttk.Entry(file_frame, textvariable=self.setting_file_var, width=50)
        setting_entry.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        ttk.Button(
            file_frame, 
            text="浏览...", 
            command=self.browse_setting_file
        ).grid(row=0, column=2, padx=5, pady=5)
        
        # Benchmark file selection
        ttk.Label(file_frame, text="基准值文件:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.benchmark_file_var = tk.StringVar()
        benchmark_entry = ttk.Entry(file_frame, textvariable=self.benchmark_file_var, width=50)
        benchmark_entry.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        ttk.Button(
            file_frame, 
            text="浏览...", 
            command=self.browse_benchmark_file
        ).grid(row=1, column=2, padx=5, pady=5)
        
        # Basic configuration
        config_frame = ttk.LabelFrame(self.content_frame, text="基本配置", padding=10)
        config_frame.pack(fill=tk.X, pady=5)
        
        # Number of patients
        ttk.Label(config_frame, text="模拟人数:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.num_patients_var = tk.StringVar(value="100000")
        num_patients_combo = ttk.Combobox(
            config_frame,
            textvariable=self.num_patients_var,
            values=["10000", "50000", "100000", "500000", "1000000"],
            width=15
        )
        num_patients_combo.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Simulation years
        ttk.Label(config_frame, text="模拟年数:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.sim_years_var = tk.StringVar(value="50")
        sim_years_combo = ttk.Combobox(
            config_frame,
            textvariable=self.sim_years_var,
            values=["10", "20", "30", "50", "100"],
            width=15
        )
        sim_years_combo.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Random seed
        ttk.Label(config_frame, text="随机种子:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.random_seed_var = tk.StringVar(value="42")
        random_seed_entry = ttk.Entry(config_frame, textvariable=self.random_seed_var, width=15)
        random_seed_entry.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)

        # 人口参数配置
        population_frame = ttk.LabelFrame(self.content_frame, text="人口参数配置", padding=10)
        population_frame.pack(fill=tk.X, pady=5)

        # 人口类型选择
        ttk.Label(population_frame, text="人口类型:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.population_type_var = tk.StringVar(value="natural")
        population_type_combo = ttk.Combobox(
            population_frame,
            textvariable=self.population_type_var,
            values=[("natural", "自然人群"), ("birth_cohort", "出生队列")],
            state="readonly",
            width=15
        )
        population_type_combo.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        population_type_combo.bind("<<ComboboxSelected>>", self._on_population_type_change)

        # 人口参数文件选择
        ttk.Label(population_frame, text="人口参数文件:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.population_file_var = tk.StringVar()
        population_entry = ttk.Entry(population_frame, textvariable=self.population_file_var, width=40)
        population_entry.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

        ttk.Button(
            population_frame,
            text="浏览...",
            command=self.browse_population_file
        ).grid(row=1, column=2, padx=5, pady=5)

        ttk.Button(
            population_frame,
            text="创建模板",
            command=self.create_population_template
        ).grid(row=1, column=3, padx=5, pady=5)

        # 使用默认参数选项
        self.use_default_population_var = tk.BooleanVar(value=True)
        default_population_check = ttk.Checkbutton(
            population_frame,
            text="使用默认人口参数",
            variable=self.use_default_population_var,
            command=self._toggle_population_file
        )
        default_population_check.grid(row=2, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)

        # 初始状态设置
        self._toggle_population_file()

    def show_calibration_step(self):
        """Show calibration configuration step."""
        # Create calibration panel
        calibration_frame = ttk.LabelFrame(self.content_frame, text="模型调参", padding=10)
        calibration_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # Calibration options
        options_frame = ttk.Frame(calibration_frame)
        options_frame.pack(fill=tk.X, pady=5)

        # Calibration method selection
        ttk.Label(options_frame, text="调参方法:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.calibration_method_var = tk.StringVar(value="auto_select")
        method_combo = ttk.Combobox(
            options_frame,
            textvariable=self.calibration_method_var,
            values=[
                ("auto_select", "自动选择最佳方法"),
                ("random_forest", "随机森林"),
                ("neural_network", "神经网络"),
                ("bayesian", "贝叶斯优化")
            ],
            state="readonly",
            width=25
        )
        method_combo.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        # Quick evaluation option
        self.quick_eval_var = tk.BooleanVar(value=True)
        quick_eval_check = ttk.Checkbutton(
            options_frame,
            text="快速评估模式（用于测试）",
            variable=self.quick_eval_var
        )
        quick_eval_check.grid(row=1, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)

        # Maximum iterations setting
        ttk.Label(options_frame, text="最大迭代次数:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.max_iterations_var = tk.IntVar(value=50)
        iterations_spinbox = ttk.Spinbox(
            options_frame,
            from_=10,
            to=500,
            textvariable=self.max_iterations_var,
            width=10
        )
        iterations_spinbox.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)

        # Tolerance setting
        ttk.Label(options_frame, text="收敛容差:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        self.tolerance_var = tk.DoubleVar(value=0.01)
        tolerance_spinbox = ttk.Spinbox(
            options_frame,
            from_=0.001,
            to=0.1,
            increment=0.001,
            textvariable=self.tolerance_var,
            width=10,
            format="%.3f"
        )
        tolerance_spinbox.grid(row=3, column=1, sticky=tk.W, padx=5, pady=5)

        # Calibration control buttons
        control_frame = ttk.Frame(calibration_frame)
        control_frame.pack(fill=tk.X, pady=10)

        self.start_calibration_button = ttk.Button(
            control_frame,
            text="开始调参",
            command=self.start_calibration
        )
        self.start_calibration_button.pack(side=tk.LEFT, padx=5)

        self.stop_calibration_button = ttk.Button(
            control_frame,
            text="停止调参",
            command=self.stop_calibration,
            state=tk.DISABLED
        )
        self.stop_calibration_button.pack(side=tk.LEFT, padx=5)

        # Progress display
        progress_frame = ttk.LabelFrame(calibration_frame, text="调参进度", padding=10)
        progress_frame.pack(fill=tk.X, pady=5)

        self.calibration_progress = ttk.Progressbar(
            progress_frame,
            orient=tk.HORIZONTAL,
            mode='indeterminate'
        )
        self.calibration_progress.pack(fill=tk.X, pady=5)

        self.calibration_status_label = ttk.Label(progress_frame, text="未开始")
        self.calibration_status_label.pack(pady=5)

        # Results display area
        results_frame = ttk.LabelFrame(calibration_frame, text="调参结果", padding=10)
        results_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # Create notebook for results tabs
        self.results_notebook = ttk.Notebook(results_frame)
        self.results_notebook.pack(fill=tk.BOTH, expand=True)

        # Parameters tab
        params_frame = ttk.Frame(self.results_notebook)
        self.results_notebook.add(params_frame, text="参数结果")

        # 参数结果显示
        self.params_text = tk.Text(params_frame, height=10, wrap=tk.WORD)
        params_scrollbar = ttk.Scrollbar(params_frame, orient="vertical", command=self.params_text.yview)
        self.params_text.configure(yscrollcommand=params_scrollbar.set)
        self.params_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        params_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Visualization tab
        viz_frame = ttk.Frame(self.results_notebook)
        self.results_notebook.add(viz_frame, text="可视化对比")

        # 可视化控制按钮
        viz_control_frame = ttk.Frame(viz_frame)
        viz_control_frame.pack(fill=tk.X, pady=5)

        ttk.Button(
            viz_control_frame,
            text="生成收敛图",
            command=self.generate_convergence_plot
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            viz_control_frame,
            text="生成参数对比图",
            command=self.generate_parameter_plot
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            viz_control_frame,
            text="生成调参对比图",
            command=self.generate_calibration_comparison_plot
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            viz_control_frame,
            text="生成完整报告",
            command=self.generate_calibration_report
        ).pack(side=tk.LEFT, padx=5)

        # 可视化显示区域
        self.viz_text = tk.Text(viz_frame, height=10, wrap=tk.WORD)
        viz_scrollbar = ttk.Scrollbar(viz_frame, orient="vertical", command=self.viz_text.yview)
        self.viz_text.configure(yscrollcommand=viz_scrollbar.set)
        self.viz_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        viz_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Log tab
        log_frame = ttk.Frame(self.results_notebook)
        self.results_notebook.add(log_frame, text="调参日志")

        # 日志控制按钮
        log_control_frame = ttk.Frame(log_frame)
        log_control_frame.pack(fill=tk.X, pady=5)

        ttk.Button(
            log_control_frame,
            text="刷新日志",
            command=self.refresh_calibration_log
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            log_control_frame,
            text="导出日志",
            command=self.export_calibration_log
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            log_control_frame,
            text="清空日志",
            command=self.clear_calibration_log
        ).pack(side=tk.LEFT, padx=5)

        # 日志显示区域
        self.log_text = tk.Text(log_frame, height=10, wrap=tk.WORD, font=("Consolas", 9))
        log_scrollbar = ttk.Scrollbar(log_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def show_screening_config_step(self):
        """Show screening configuration step."""
        # Create screening configuration panel
        screening_frame = ttk.LabelFrame(self.content_frame, text="筛查策略配置", padding=10)
        screening_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # Screening tool selection
        tool_frame = ttk.LabelFrame(screening_frame, text="筛查工具选择", padding=10)
        tool_frame.pack(fill=tk.X, pady=5)

        # Primary screening tool
        ttk.Label(tool_frame, text="主要筛查工具:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.primary_tool_var = tk.StringVar(value="colonoscopy")
        primary_tool_combo = ttk.Combobox(
            tool_frame,
            textvariable=self.primary_tool_var,
            values=[
                ("colonoscopy", "结肠镜检查"),
                ("fit", "粪便免疫化学试验(FIT)"),
                ("sigmoidoscopy", "乙状结肠镜检查"),
                ("ct_colonography", "CT结肠成像"),
                ("other", "其他")
            ],
            state="readonly",
            width=25
        )
        primary_tool_combo.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        # Sequential screening option
        self.sequential_screening_var = tk.BooleanVar(value=False)
        sequential_check = ttk.Checkbutton(
            tool_frame,
            text="启用贯序筛查",
            variable=self.sequential_screening_var,
            command=self.toggle_sequential_screening
        )
        sequential_check.grid(row=1, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)

        # Secondary screening tool (only visible when sequential is enabled)
        self.secondary_tool_label = ttk.Label(tool_frame, text="次要筛查工具:")
        self.secondary_tool_var = tk.StringVar(value="fit")
        self.secondary_tool_combo = ttk.Combobox(
            tool_frame,
            textvariable=self.secondary_tool_var,
            values=[
                ("fit", "粪便免疫化学试验(FIT)"),
                ("sigmoidoscopy", "乙状结肠镜检查"),
                ("ct_colonography", "CT结肠成像"),
                ("other", "其他")
            ],
            state="readonly",
            width=25
        )

        # Screening parameters
        params_frame = ttk.LabelFrame(screening_frame, text="筛查参数", padding=10)
        params_frame.pack(fill=tk.X, pady=5)

        # Age range
        ttk.Label(params_frame, text="筛查起始年龄:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.start_age_var = tk.StringVar(value="50")
        start_age_combo = ttk.Combobox(
            params_frame,
            textvariable=self.start_age_var,
            values=["40", "45", "50", "55"],
            width=10
        )
        start_age_combo.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        ttk.Label(params_frame, text="筛查结束年龄:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        self.end_age_var = tk.StringVar(value="75")
        end_age_combo = ttk.Combobox(
            params_frame,
            textvariable=self.end_age_var,
            values=["70", "75", "80", "85"],
            width=10
        )
        end_age_combo.grid(row=0, column=3, sticky=tk.W, padx=5, pady=5)

        # Performance parameters
        perf_frame = ttk.LabelFrame(screening_frame, text="筛查工具性能参数", padding=10)
        perf_frame.pack(fill=tk.X, pady=5)

        # Sensitivity for adenoma
        ttk.Label(perf_frame, text="腺瘤灵敏度 (%):").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.adenoma_sensitivity_var = tk.StringVar(value="85")
        adenoma_sens_entry = ttk.Entry(perf_frame, textvariable=self.adenoma_sensitivity_var, width=10)
        adenoma_sens_entry.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        # Sensitivity for cancer
        ttk.Label(perf_frame, text="癌症灵敏度 (%):").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        self.cancer_sensitivity_var = tk.StringVar(value="95")
        cancer_sens_entry = ttk.Entry(perf_frame, textvariable=self.cancer_sensitivity_var, width=10)
        cancer_sens_entry.grid(row=0, column=3, sticky=tk.W, padx=5, pady=5)

        # Specificity
        ttk.Label(perf_frame, text="特异度 (%):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.specificity_var = tk.StringVar(value="90")
        specificity_entry = ttk.Entry(perf_frame, textvariable=self.specificity_var, width=10)
        specificity_entry.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

        # Compliance
        ttk.Label(perf_frame, text="依从性 (%):").grid(row=1, column=2, sticky=tk.W, padx=5, pady=5)
        self.compliance_var = tk.StringVar(value="70")
        compliance_entry = ttk.Entry(perf_frame, textvariable=self.compliance_var, width=10)
        compliance_entry.grid(row=1, column=3, sticky=tk.W, padx=5, pady=5)

        # Diagnostic colonoscopy compliance for positive results (for non-colonoscopy tools)
        self.diagnostic_compliance_label = ttk.Label(perf_frame, text="阳性后肠镜依从性 (%):")
        self.diagnostic_compliance_var = tk.StringVar(value="80")
        self.diagnostic_compliance_entry = ttk.Entry(perf_frame, textvariable=self.diagnostic_compliance_var, width=10)

        # Initially hide this field, will show based on primary tool selection
        self.primary_tool_var.trace('w', self.on_primary_tool_change)

        # Secondary screening parameters frame (initially hidden)
        self.secondary_params_frame = ttk.LabelFrame(screening_frame, text="次要筛查工具参数", padding=10)

        # Secondary tool age range
        ttk.Label(self.secondary_params_frame, text="次要工具起始年龄:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.secondary_start_age_var = tk.StringVar(value="50")
        secondary_start_age_combo = ttk.Combobox(
            self.secondary_params_frame,
            textvariable=self.secondary_start_age_var,
            values=["40", "45", "50", "55"],
            width=10
        )
        secondary_start_age_combo.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        ttk.Label(self.secondary_params_frame, text="次要工具结束年龄:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        self.secondary_end_age_var = tk.StringVar(value="75")
        secondary_end_age_combo = ttk.Combobox(
            self.secondary_params_frame,
            textvariable=self.secondary_end_age_var,
            values=["70", "75", "80", "85"],
            width=10
        )
        secondary_end_age_combo.grid(row=0, column=3, sticky=tk.W, padx=5, pady=5)

        # Secondary tool performance parameters
        ttk.Label(self.secondary_params_frame, text="次要工具腺瘤灵敏度 (%):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.secondary_adenoma_sensitivity_var = tk.StringVar(value="40")
        secondary_adenoma_sens_entry = ttk.Entry(self.secondary_params_frame, textvariable=self.secondary_adenoma_sensitivity_var, width=10)
        secondary_adenoma_sens_entry.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

        ttk.Label(self.secondary_params_frame, text="次要工具癌症灵敏度 (%):").grid(row=1, column=2, sticky=tk.W, padx=5, pady=5)
        self.secondary_cancer_sensitivity_var = tk.StringVar(value="70")
        secondary_cancer_sens_entry = ttk.Entry(self.secondary_params_frame, textvariable=self.secondary_cancer_sensitivity_var, width=10)
        secondary_cancer_sens_entry.grid(row=1, column=3, sticky=tk.W, padx=5, pady=5)

        ttk.Label(self.secondary_params_frame, text="次要工具特异度 (%):").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.secondary_specificity_var = tk.StringVar(value="95")
        secondary_specificity_entry = ttk.Entry(self.secondary_params_frame, textvariable=self.secondary_specificity_var, width=10)
        secondary_specificity_entry.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)

        ttk.Label(self.secondary_params_frame, text="次要工具依从性 (%):").grid(row=2, column=2, sticky=tk.W, padx=5, pady=5)
        self.secondary_compliance_var = tk.StringVar(value="85")
        secondary_compliance_entry = ttk.Entry(self.secondary_params_frame, textvariable=self.secondary_compliance_var, width=10)
        secondary_compliance_entry.grid(row=2, column=3, sticky=tk.W, padx=5, pady=5)

        # Secondary tool diagnostic colonoscopy compliance
        ttk.Label(self.secondary_params_frame, text="次要工具阳性后肠镜依从性 (%):").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        self.secondary_diagnostic_compliance_var = tk.StringVar(value="80")
        secondary_diagnostic_compliance_entry = ttk.Entry(self.secondary_params_frame, textvariable=self.secondary_diagnostic_compliance_var, width=10)
        secondary_diagnostic_compliance_entry.grid(row=3, column=1, sticky=tk.W, padx=5, pady=5)

        # Initialize visibility based on current selections
        self.on_primary_tool_change()
        self.toggle_sequential_screening()

    def show_output_config_step(self):
        """Show output configuration step."""
        # Create output configuration panel
        output_frame = ttk.LabelFrame(self.content_frame, text="输出配置", padding=10)
        output_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # Output format selection
        format_frame = ttk.LabelFrame(output_frame, text="输出格式", padding=10)
        format_frame.pack(fill=tk.X, pady=5)

        # Output formats
        self.output_formats = {
            "excel": tk.BooleanVar(value=True),
            "pdf": tk.BooleanVar(value=True),
            "csv": tk.BooleanVar(value=False),
            "json": tk.BooleanVar(value=False),
            "matlab": tk.BooleanVar(value=False)
        }

        format_labels = {
            "excel": "Excel报告 (.xlsx)",
            "pdf": "PDF报告 (.pdf)",
            "csv": "CSV数据 (.csv)",
            "json": "JSON数据 (.json)",
            "matlab": "MATLAB数据 (.mat)"
        }

        row = 0
        for format_key, format_var in self.output_formats.items():
            ttk.Checkbutton(
                format_frame,
                text=format_labels[format_key],
                variable=format_var
            ).grid(row=row // 2, column=row % 2, sticky=tk.W, padx=10, pady=5)
            row += 1

        # Output directory selection
        dir_frame = ttk.LabelFrame(output_frame, text="输出目录", padding=10)
        dir_frame.pack(fill=tk.X, pady=5)

        ttk.Label(dir_frame, text="输出目录:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.output_dir_var = tk.StringVar(value="./results")
        output_dir_entry = ttk.Entry(dir_frame, textvariable=self.output_dir_var, width=50)
        output_dir_entry.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        ttk.Button(
            dir_frame,
            text="浏览...",
            command=self.browse_output_directory
        ).grid(row=0, column=2, padx=5, pady=5)

        # Output content selection
        content_frame = ttk.LabelFrame(output_frame, text="输出内容", padding=10)
        content_frame.pack(fill=tk.X, pady=5)

        self.output_contents = {
            "summary": tk.BooleanVar(value=True),
            "detailed_results": tk.BooleanVar(value=True),
            "charts": tk.BooleanVar(value=True),
            "calibration_results": tk.BooleanVar(value=True),
            "screening_analysis": tk.BooleanVar(value=True),
            "cost_effectiveness": tk.BooleanVar(value=False)
        }

        content_labels = {
            "summary": "摘要统计",
            "detailed_results": "详细结果",
            "charts": "图表可视化",
            "calibration_results": "调参结果",
            "screening_analysis": "筛查分析",
            "cost_effectiveness": "成本效益分析"
        }

        row = 0
        for content_key, content_var in self.output_contents.items():
            ttk.Checkbutton(
                content_frame,
                text=content_labels[content_key],
                variable=content_var
            ).grid(row=row // 2, column=row % 2, sticky=tk.W, padx=10, pady=5)
            row += 1

        # File naming options
        naming_frame = ttk.LabelFrame(output_frame, text="文件命名", padding=10)
        naming_frame.pack(fill=tk.X, pady=5)

        ttk.Label(naming_frame, text="文件名前缀:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.filename_prefix_var = tk.StringVar(value="cmost_simulation")
        filename_entry = ttk.Entry(naming_frame, textvariable=self.filename_prefix_var, width=30)
        filename_entry.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        self.include_timestamp_var = tk.BooleanVar(value=True)
        timestamp_check = ttk.Checkbutton(
            naming_frame,
            text="包含时间戳",
            variable=self.include_timestamp_var
        )
        timestamp_check.grid(row=1, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)

    def show_simulation_step(self):
        """Show simulation execution step."""
        # Create simulation panel
        sim_frame = ttk.LabelFrame(self.content_frame, text="模拟执行", padding=10)
        sim_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # Configuration summary
        summary_frame = ttk.LabelFrame(sim_frame, text="配置摘要", padding=10)
        summary_frame.pack(fill=tk.X, pady=5)

        # Create summary text widget
        summary_text = tk.Text(summary_frame, height=8, wrap=tk.WORD)
        summary_text.pack(fill=tk.X, pady=5)

        # Generate configuration summary
        summary_content = self.generate_config_summary()
        summary_text.insert(tk.END, summary_content)
        summary_text.config(state=tk.DISABLED)

        # Simulation control
        control_frame = ttk.LabelFrame(sim_frame, text="模拟控制", padding=10)
        control_frame.pack(fill=tk.X, pady=5)

        # Simulation buttons
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill=tk.X, pady=5)

        self.start_simulation_button = ttk.Button(
            button_frame,
            text="开始模拟",
            command=self.start_simulation
        )
        self.start_simulation_button.pack(side=tk.LEFT, padx=5)

        self.pause_simulation_button = ttk.Button(
            button_frame,
            text="暂停模拟",
            command=self.pause_simulation,
            state=tk.DISABLED
        )
        self.pause_simulation_button.pack(side=tk.LEFT, padx=5)

        self.stop_simulation_button = ttk.Button(
            button_frame,
            text="停止模拟",
            command=self.stop_simulation,
            state=tk.DISABLED
        )
        self.stop_simulation_button.pack(side=tk.LEFT, padx=5)

        # Progress display
        progress_frame = ttk.LabelFrame(sim_frame, text="模拟进度", padding=10)
        progress_frame.pack(fill=tk.X, pady=5)

        self.simulation_progress = ttk.Progressbar(
            progress_frame,
            orient=tk.HORIZONTAL,
            mode='determinate'
        )
        self.simulation_progress.pack(fill=tk.X, pady=5)

        # Progress information
        info_frame = ttk.Frame(progress_frame)
        info_frame.pack(fill=tk.X, pady=5)

        ttk.Label(info_frame, text="状态:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.sim_status_label = ttk.Label(info_frame, text="未开始")
        self.sim_status_label.grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)

        ttk.Label(info_frame, text="已处理患者:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=2)
        self.patients_processed_label = ttk.Label(info_frame, text="0 / 0")
        self.patients_processed_label.grid(row=0, column=3, sticky=tk.W, padx=5, pady=2)

        ttk.Label(info_frame, text="已用时间:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.elapsed_time_label = ttk.Label(info_frame, text="00:00:00")
        self.elapsed_time_label.grid(row=1, column=1, sticky=tk.W, padx=5, pady=2)

        ttk.Label(info_frame, text="预计剩余:").grid(row=1, column=2, sticky=tk.W, padx=5, pady=2)
        self.eta_label = ttk.Label(info_frame, text="--:--:--")
        self.eta_label.grid(row=1, column=3, sticky=tk.W, padx=5, pady=2)

        # Results preview
        results_frame = ttk.LabelFrame(sim_frame, text="结果预览", padding=10)
        results_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # Results will be displayed here during simulation
        self.results_text = tk.Text(results_frame, height=10, wrap=tk.WORD)
        self.results_text.pack(fill=tk.BOTH, expand=True, pady=5)

        # Add scrollbar
        scrollbar = ttk.Scrollbar(results_frame, orient="vertical", command=self.results_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.results_text.configure(yscrollcommand=scrollbar.set)

    # Event handlers
    def browse_setting_file(self):
        """Browse for setting file."""
        file_path = filedialog.askopenfilename(
            title="选择Setting文件",
            filetypes=[
                ("Excel files", "*.xlsx *.xls"),
                ("JSON files", "*.json"),
                ("YAML files", "*.yaml *.yml"),
                ("All files", "*.*")
            ]
        )
        if file_path:
            self.setting_file_var.set(file_path)
            self.setting_file_path = file_path
            self.set_status(f"已选择Setting文件: {Path(file_path).name}")

    def browse_benchmark_file(self):
        """Browse for benchmark file."""
        file_path = filedialog.askopenfilename(
            title="选择基准值文件",
            filetypes=[
                ("Excel files", "*.xlsx *.xls"),
                ("CSV files", "*.csv"),
                ("JSON files", "*.json"),
                ("MATLAB files", "*.mat"),
                ("All files", "*.*")
            ]
        )
        if file_path:
            self.benchmark_file_var.set(file_path)
            self.benchmark_file_path = file_path
            self.set_status(f"已选择基准值文件: {Path(file_path).name}")

    def browse_output_directory(self):
        """Browse for output directory."""
        dir_path = filedialog.askdirectory(title="选择输出目录")
        if dir_path:
            self.output_dir_var.set(dir_path)
            self.set_status(f"已选择输出目录: {dir_path}")

    def on_primary_tool_change(self, *args):
        """Handle primary screening tool change."""
        primary_tool = self.primary_tool_var.get()

        # Show diagnostic colonoscopy compliance for non-colonoscopy tools
        if primary_tool != "colonoscopy":
            self.diagnostic_compliance_label.grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
            self.diagnostic_compliance_entry.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
        else:
            self.diagnostic_compliance_label.grid_remove()
            self.diagnostic_compliance_entry.grid_remove()

    def toggle_sequential_screening(self):
        """Toggle sequential screening options."""
        if self.sequential_screening_var.get():
            # Show secondary tool selection
            self.secondary_tool_label.grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
            self.secondary_tool_combo.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)

            # Show secondary tool parameters frame
            self.secondary_params_frame.pack(fill=tk.X, pady=5)
        else:
            # Hide secondary tool selection
            self.secondary_tool_label.grid_remove()
            self.secondary_tool_combo.grid_remove()

            # Hide secondary tool parameters frame
            self.secondary_params_frame.pack_forget()

    def previous_step(self):
        """Go to previous step."""
        if self.current_step > 0:
            self.show_step(self.current_step - 1)

    def next_step(self):
        """Go to next step."""
        if self.validate_current_step():
            if self.current_step < len(self.workflow_steps) - 1:
                self.show_step(self.current_step + 1)

    def skip_calibration(self):
        """Skip calibration step."""
        if messagebox.askyesno("跳过调参", "确定要跳过调参步骤吗？\n这将使用默认参数进行模拟。"):
            self.calibration_results = None
            self.show_step(2)  # Go to screening config

    def reset_workflow(self):
        """Reset the entire workflow."""
        if messagebox.askyesno("重置工作流程", "确定要重置整个工作流程吗？\n所有配置将被清除。"):
            # Reset all variables
            self.setting_file_path = None
            self.benchmark_file_path = None
            self.calibration_results = None
            self.screening_config = {}
            self.output_config = {}

            # Reset UI variables
            if hasattr(self, 'setting_file_var'):
                self.setting_file_var.set("")
            if hasattr(self, 'benchmark_file_var'):
                self.benchmark_file_var.set("")

            # Go back to first step
            self.show_step(0)
            self.set_status("工作流程已重置")

    def validate_current_step(self) -> bool:
        """
        Validate the current step configuration.

        Returns:
            True if current step is valid, False otherwise
        """
        if self.current_step == 0:
            # File selection step
            if not self.setting_file_path:
                messagebox.showerror("验证错误", "请选择Setting文件")
                return False
            if not self.benchmark_file_path:
                messagebox.showerror("验证错误", "请选择基准值文件")
                return False
            try:
                int(self.num_patients_var.get())
                int(self.sim_years_var.get())
                int(self.random_seed_var.get())
            except ValueError:
                messagebox.showerror("验证错误", "请输入有效的数值")
                return False

        elif self.current_step == 1:
            # Calibration step - no validation needed if skipped
            pass

        elif self.current_step == 2:
            # Screening configuration step
            try:
                start_age = int(self.start_age_var.get())
                end_age = int(self.end_age_var.get())
                if start_age >= end_age:
                    messagebox.showerror("验证错误", "筛查起始年龄必须小于结束年龄")
                    return False

                # Validate performance parameters
                float(self.adenoma_sensitivity_var.get())
                float(self.cancer_sensitivity_var.get())
                float(self.specificity_var.get())
                float(self.compliance_var.get())

                # Validate diagnostic compliance for non-colonoscopy tools
                if self.primary_tool_var.get() != "colonoscopy":
                    float(self.diagnostic_compliance_var.get())

                # Validate secondary tool parameters if sequential screening is enabled
                if self.sequential_screening_var.get():
                    secondary_start_age = int(self.secondary_start_age_var.get())
                    secondary_end_age = int(self.secondary_end_age_var.get())
                    if secondary_start_age >= secondary_end_age:
                        messagebox.showerror("验证错误", "次要筛查工具起始年龄必须小于结束年龄")
                        return False

                    float(self.secondary_adenoma_sensitivity_var.get())
                    float(self.secondary_cancer_sensitivity_var.get())
                    float(self.secondary_specificity_var.get())
                    float(self.secondary_compliance_var.get())
                    float(self.secondary_diagnostic_compliance_var.get())

            except ValueError:
                messagebox.showerror("验证错误", "请输入有效的筛查参数")
                return False

        elif self.current_step == 3:
            # Output configuration step
            if not any(var.get() for var in self.output_formats.values()):
                messagebox.showerror("验证错误", "请至少选择一种输出格式")
                return False
            if not self.output_dir_var.get():
                messagebox.showerror("验证错误", "请选择输出目录")
                return False

        return True

    def generate_config_summary(self) -> str:
        """
        Generate a summary of the current configuration.

        Returns:
            Configuration summary text
        """
        summary = "=== 配置摘要 ===\n\n"

        # Basic configuration
        summary += "基本配置:\n"
        summary += f"  Setting文件: {Path(self.setting_file_path).name if self.setting_file_path else '未选择'}\n"
        summary += f"  基准值文件: {Path(self.benchmark_file_path).name if self.benchmark_file_path else '未选择'}\n"
        summary += f"  模拟人数: {self.num_patients_var.get()}\n"
        summary += f"  模拟年数: {self.sim_years_var.get()}\n"
        summary += f"  随机种子: {self.random_seed_var.get()}\n\n"

        # Calibration
        summary += "调参配置:\n"
        if self.calibration_results:
            summary += f"  调参方法: {self.calibration_method_var.get()}\n"
            summary += f"  调参状态: 已完成\n"
        else:
            summary += "  调参状态: 跳过或未完成\n"
        summary += "\n"

        # Screening configuration
        summary += "筛查配置:\n"
        summary += f"  主要筛查工具: {self.primary_tool_var.get()}\n"
        summary += f"  主要工具年龄范围: {self.start_age_var.get()} - {self.end_age_var.get()}岁\n"
        summary += f"  主要工具腺瘤灵敏度: {self.adenoma_sensitivity_var.get()}%\n"
        summary += f"  主要工具癌症灵敏度: {self.cancer_sensitivity_var.get()}%\n"
        summary += f"  主要工具特异度: {self.specificity_var.get()}%\n"
        summary += f"  主要工具依从性: {self.compliance_var.get()}%\n"

        # Add diagnostic compliance for non-colonoscopy tools
        if self.primary_tool_var.get() != "colonoscopy":
            summary += f"  主要工具阳性后肠镜依从性: {self.diagnostic_compliance_var.get()}%\n"

        # Add secondary tool information if sequential screening is enabled
        if self.sequential_screening_var.get():
            summary += f"  次要筛查工具: {self.secondary_tool_var.get()}\n"
            summary += f"  次要工具年龄范围: {self.secondary_start_age_var.get()} - {self.secondary_end_age_var.get()}岁\n"
            summary += f"  次要工具腺瘤灵敏度: {self.secondary_adenoma_sensitivity_var.get()}%\n"
            summary += f"  次要工具癌症灵敏度: {self.secondary_cancer_sensitivity_var.get()}%\n"
            summary += f"  次要工具特异度: {self.secondary_specificity_var.get()}%\n"
            summary += f"  次要工具依从性: {self.secondary_compliance_var.get()}%\n"
            summary += f"  次要工具阳性后肠镜依从性: {self.secondary_diagnostic_compliance_var.get()}%\n"

        summary += "\n"

        # Output configuration
        summary += "输出配置:\n"
        selected_formats = [fmt for fmt, var in self.output_formats.items() if var.get()]
        summary += f"  输出格式: {', '.join(selected_formats)}\n"
        summary += f"  输出目录: {self.output_dir_var.get()}\n"
        summary += f"  文件名前缀: {self.filename_prefix_var.get()}\n"

        return summary

    # Core functionality methods
    def start_calibration(self):
        """Start the calibration process."""
        if not self.validate_calibration_inputs():
            return

        self.set_status("开始调参...")
        self.start_calibration_button.configure(state=tk.DISABLED)
        self.stop_calibration_button.configure(state=tk.NORMAL)
        self.calibration_progress.start()

        # Start calibration in a separate thread
        calibration_thread = threading.Thread(target=self._run_calibration)
        calibration_thread.daemon = True
        calibration_thread.start()

    def stop_calibration(self):
        """Stop the calibration process."""
        self.set_status("停止调参...")
        self.calibration_progress.stop()
        self.start_calibration_button.configure(state=tk.NORMAL)
        self.stop_calibration_button.configure(state=tk.DISABLED)
        self.calibration_status_label.configure(text="已停止")

    def _run_calibration(self):
        """Run calibration in a separate thread."""
        try:
            # 初始化日志记录
            try:
                from ..calibration.logging_manager import calibration_logger
                method_name = self.calibration_method_var.get()
                initial_params = {
                    'early_adenoma_rate': 0.020,
                    'advanced_adenoma_rate': 0.010,
                    'cancer_rate': 0.001
                }
                calibration_logger.start_calibration_session(method_name, initial_params)
            except Exception as e:
                print(f"日志初始化失败: {e}")

            # Load benchmark data
            self.root.after(0, lambda: self.calibration_status_label.configure(text="加载基准值数据..."))

            # Here you would load the actual benchmark data
            # For now, we'll use mock data
            benchmarks = self._load_benchmark_data()

            # Create calibrator
            self.root.after(0, lambda: self.calibration_status_label.configure(text="初始化校准器..."))
            if IntegratedCalibrator:
                calibrator = IntegratedCalibrator(
                    benchmarks=benchmarks,
                    output_dir="calibration_results"
                )
            else:
                # Fallback if calibrator is not available
                calibrator = None

            # Run calibration
            self.root.after(0, lambda: self.calibration_status_label.configure(text="执行调参..."))
            method = self.calibration_method_var.get()

            # Use quick evaluation if selected
            quick_eval = self.quick_eval_var.get()

            # 模拟调参过程，生成历史数据
            import time
            import random

            # 生成模拟的调参历史
            history = []
            current_params = {
                'early_adenoma_rate': 0.020,
                'advanced_adenoma_rate': 0.010,
                'cancer_rate': 0.001
            }
            current_error = 0.5

            # 获取用户设置的迭代次数
            max_iterations = self.max_iterations_var.get()
            tolerance = self.tolerance_var.get()

            # 模拟多次迭代
            iterations = min(10, max_iterations) if quick_eval else max_iterations
            for i in range(iterations):
                # 更新状态
                self.root.after(0, lambda i=i: self.calibration_status_label.configure(
                    text=f"调参迭代 {i+1}/{iterations}..."
                ))

                # 模拟参数调整
                for param in current_params:
                    current_params[param] += random.uniform(-0.002, 0.002)
                    current_params[param] = max(0.0001, current_params[param])  # 确保正值

                # 模拟误差减少
                current_error *= random.uniform(0.7, 0.95)

                # 检查收敛条件
                if current_error < tolerance:
                    self.root.after(0, lambda: self.calibration_status_label.configure(
                        text=f"已收敛 (迭代 {i+1}/{iterations})"
                    ))
                    break

                # 记录历史
                iteration_data = {
                    'iteration': i,
                    'parameters': current_params.copy(),
                    'error': current_error,
                    'execution_time': random.uniform(0.5, 2.0),
                    'timestamp': time.time()
                }
                history.append(iteration_data)

                # 记录到日志
                try:
                    calibration_logger.log_iteration(
                        i, current_params.copy(), current_error,
                        iteration_data['execution_time']
                    )
                except:
                    pass

                # 模拟处理时间
                time.sleep(0.3 if quick_eval else 0.8)

            # 最终结果
            final_results = {
                'method': method,
                'parameters': current_params.copy(),
                'error': current_error,
                'execution_time': sum(h['execution_time'] for h in history),
                'history': history,
                'convergence_info': {
                    'converged': current_error < 0.1,
                    'iterations': len(history),
                    'final_error': current_error
                }
            }

            self.calibration_results = final_results

            # 结束日志会话
            try:
                calibration_logger.end_calibration_session(final_results)
            except:
                pass

            # Update UI in main thread
            self.root.after(0, self._calibration_completed)

        except Exception as e:
            # 记录错误到日志
            try:
                from ..calibration.logging_manager import calibration_logger
                calibration_logger.log_error(str(e), e)
            except:
                pass
            self.root.after(0, lambda: self._calibration_error(str(e)))

    def _calibration_completed(self):
        """Handle calibration completion."""
        self.calibration_progress.stop()
        self.start_calibration_button.configure(state=tk.NORMAL)
        self.stop_calibration_button.configure(state=tk.DISABLED)
        self.calibration_status_label.configure(text="调参完成")

        # Display results
        self._display_calibration_results()

        self.set_status("调参完成")
        messagebox.showinfo("调参完成", "模型调参已成功完成！")

    def _calibration_error(self, error_message: str):
        """Handle calibration error."""
        self.calibration_progress.stop()
        self.start_calibration_button.configure(state=tk.NORMAL)
        self.stop_calibration_button.configure(state=tk.DISABLED)
        self.calibration_status_label.configure(text="调参失败")

        self.set_status("调参失败")
        messagebox.showerror("调参错误", f"调参过程中发生错误:\n{error_message}")

    def _display_calibration_results(self):
        """Display calibration results in the UI."""
        if not self.calibration_results:
            return

        # 更新参数结果标签页
        self._update_parameters_tab()

        # 更新可视化标签页
        self._update_visualization_tab()

        # 更新日志标签页
        self._update_log_tab()

    def _update_parameters_tab(self):
        """更新参数结果标签页"""
        if not hasattr(self, 'params_text'):
            return

        self.params_text.config(state=tk.NORMAL)
        self.params_text.delete(1.0, tk.END)

        params_content = "=== 调参结果参数 ===\n\n"

        if 'parameters' in self.calibration_results:
            for param, value in self.calibration_results['parameters'].items():
                params_content += f"{param}: {value:.6f}\n"

        params_content += f"\n=== 调参统计信息 ===\n"
        params_content += f"最终误差: {self.calibration_results.get('error', 'N/A')}\n"
        params_content += f"执行时间: {self.calibration_results.get('execution_time', 'N/A')} 秒\n"
        params_content += f"调参方法: {self.calibration_results.get('method', 'N/A')}\n"

        if 'convergence_info' in self.calibration_results:
            conv_info = self.calibration_results['convergence_info']
            params_content += f"是否收敛: {'是' if conv_info.get('converged', False) else '否'}\n"
            if 'iterations' in conv_info:
                params_content += f"迭代次数: {conv_info['iterations']}\n"

        self.params_text.insert(tk.END, params_content)
        self.params_text.config(state=tk.DISABLED)

    def _update_visualization_tab(self):
        """更新可视化标签页"""
        if not hasattr(self, 'viz_text'):
            return

        self.viz_text.config(state=tk.NORMAL)
        self.viz_text.delete(1.0, tk.END)

        viz_content = "=== 调参可视化功能 ===\n\n"
        viz_content += "点击下方按钮生成可视化图表:\n\n"
        viz_content += "• 生成收敛图 - 显示误差收敛过程和参数变化\n"
        viz_content += "• 生成参数对比图 - 显示最优参数值和重要性分析\n"
        viz_content += "• 生成调参对比图 - 点线图+置信区间显示基准值vs校准后模型值(按年龄性别分组)\n"
        viz_content += "• 生成完整报告 - 创建包含所有图表的HTML报告\n\n"

        if 'history' in self.calibration_results and self.calibration_results['history']:
            viz_content += f"当前有 {len(self.calibration_results['history'])} 条调参历史记录可用于可视化\n"
        else:
            viz_content += "注意: 当前没有调参历史记录，某些可视化功能可能受限\n"

        self.viz_text.insert(tk.END, viz_content)
        self.viz_text.config(state=tk.DISABLED)

    def _update_log_tab(self):
        """更新日志标签页"""
        if not hasattr(self, 'log_text'):
            return

        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)

        log_content = "=== 调参日志 ===\n\n"

        try:
            from ..calibration.logging_manager import calibration_logger

            # 获取当前会话摘要
            summary = calibration_logger.get_session_summary()

            if summary.get('status') == 'no_iterations':
                log_content += "当前没有活动的调参会话\n\n"
                log_content += "提示: 调参日志会在调参过程中自动记录\n"
                log_content += "包括参数变化、误差变化、收敛状态等信息\n"
            else:
                log_content += f"调参方法: {summary.get('method', 'N/A')}\n"
                log_content += f"总迭代次数: {summary.get('total_iterations', 0)}\n"
                log_content += f"开始时间: {summary.get('start_time', 'N/A')}\n"
                log_content += f"初始误差: {summary.get('initial_error', 'N/A')}\n"
                log_content += f"最终误差: {summary.get('final_error', 'N/A')}\n"
                log_content += f"最佳误差: {summary.get('best_error', 'N/A')}\n"
                log_content += f"误差改善: {summary.get('error_improvement', 0):.6f}\n"
                log_content += f"是否收敛: {'是' if summary.get('convergence_achieved') else '否'}\n"

        except Exception as e:
            log_content += f"获取日志信息时出错: {str(e)}\n"

        log_content += "\n使用下方按钮管理调参日志:\n"
        log_content += "• 刷新日志 - 更新当前显示的日志信息\n"
        log_content += "• 导出日志 - 将日志导出为JSON文件\n"
        log_content += "• 清空日志 - 清空当前显示内容\n"

        self.log_text.insert(tk.END, log_content)
        self.log_text.config(state=tk.DISABLED)

    def validate_calibration_inputs(self) -> bool:
        """Validate calibration inputs."""
        if not self.benchmark_file_path:
            messagebox.showerror("验证错误", "请先选择基准值文件")
            return False
        return True

    def _load_benchmark_data(self) -> Dict:
        """Load benchmark data from file."""
        # This is a placeholder - actual implementation would load from file
        return {
            'early_adenoma_prevalence': {
                'M': {50: 30.0, 60: 40.0, 70: 45.0},
                'F': {50: 25.0, 60: 35.0, 70: 40.0}
            },
            'advanced_adenoma_prevalence': {
                'M': {50: 8.0, 60: 12.0, 70: 15.0},
                'F': {50: 6.0, 60: 10.0, 70: 13.0}
            },
            'cancer_incidence': {
                'M': {50: 50.0, 60: 100.0, 70: 150.0},
                'F': {50: 40.0, 60: 80.0, 70: 120.0}
            }
        }

    def execute_simulation(self):
        """Execute the main simulation."""
        if not self.validate_current_step():
            return

        self.set_status("准备执行模拟...")
        messagebox.showinfo("开始模拟", "模拟即将开始，这可能需要一些时间。")

        # This would start the actual simulation
        # For now, we'll show a placeholder
        self.start_simulation()

    def start_simulation(self):
        """Start the simulation process."""
        self.set_status("开始模拟...")

        # Reset simulation control variables
        self.simulation_running = True
        self.simulation_paused = False
        self.simulation_stopped = False

        # 记录开始时间
        import time
        self.simulation_start_time = time.time()

        # Update button states
        self.start_simulation_button.configure(state=tk.DISABLED)
        self.pause_simulation_button.configure(state=tk.NORMAL, text="暂停模拟")
        self.stop_simulation_button.configure(state=tk.NORMAL)

        # 开始时间更新循环
        self._update_time_display()

        # Start simulation in a separate thread
        self.simulation_thread = threading.Thread(target=self._run_simulation)
        self.simulation_thread.daemon = True
        self.simulation_thread.start()

    def pause_simulation(self):
        """Pause or resume the simulation."""
        if not self.simulation_paused:
            # Pause the simulation
            self.simulation_paused = True
            self.set_status("模拟已暂停")
            self.pause_simulation_button.configure(text="恢复模拟")
            self.sim_status_label.configure(text="已暂停")
        else:
            # Resume the simulation
            self.simulation_paused = False
            self.set_status("模拟已恢复")
            self.pause_simulation_button.configure(text="暂停模拟")
            self.sim_status_label.configure(text="运行中...")

    def stop_simulation(self):
        """Stop the simulation."""
        self.set_status("停止模拟...")

        # Set stop flag
        self.simulation_stopped = True
        self.simulation_running = False
        self.simulation_paused = False

        # Update UI
        self.sim_status_label.configure(text="已停止")
        self.start_simulation_button.configure(state=tk.NORMAL)
        self.pause_simulation_button.configure(state=tk.DISABLED, text="暂停模拟")
        self.stop_simulation_button.configure(state=tk.DISABLED)

        # 停止时间更新
        self.eta_label.configure(text="--:--:--")

        # Display stop message in results
        self.results_text.delete(1.0, tk.END)
        self.results_text.insert(tk.END, "模拟已被用户停止。\n\n")
        self.results_text.insert(tk.END, "如需查看结果，请重新运行模拟。")

    def _run_simulation(self):
        """Run simulation in a separate thread."""
        try:
            # Mock simulation progress
            total_patients = int(self.num_patients_var.get())

            for i in range(total_patients + 1):
                # Check if simulation should be stopped
                if self.simulation_stopped:
                    return

                # Check if simulation is paused
                while self.simulation_paused and not self.simulation_stopped:
                    import time
                    time.sleep(0.1)  # Wait while paused

                # Check again after pause in case stop was requested during pause
                if self.simulation_stopped:
                    return

                if i % 1000 == 0:  # Update every 1000 patients
                    progress = (i / total_patients) * 100
                    self.root.after(0, lambda p=progress, i=i: self._update_simulation_progress(p, i, total_patients))

                # Simulate processing time
                import time
                time.sleep(0.001)  # Very short delay for demo

            # Check if simulation was stopped before completion
            if not self.simulation_stopped:
                # Simulation completed normally
                self.simulation_running = False
                self.root.after(0, self._simulation_completed)

        except Exception as e:
            self.simulation_running = False
            self.root.after(0, lambda: self._simulation_error(str(e)))

    def _update_simulation_progress(self, progress: float, processed: int, total: int):
        """Update simulation progress in UI."""
        self.simulation_progress['value'] = progress
        self.patients_processed_label.configure(text=f"{processed:,} / {total:,}")

        # Update status based on current state
        if self.simulation_paused:
            self.sim_status_label.configure(text="已暂停")
        elif self.simulation_running:
            self.sim_status_label.configure(text="运行中...")
        else:
            self.sim_status_label.configure(text="已停止")

    def _update_time_display(self):
        """更新时间显示"""
        if not self.simulation_running:
            return

        import time
        if hasattr(self, 'simulation_start_time'):
            # 计算已用时间
            elapsed_seconds = time.time() - self.simulation_start_time
            elapsed_str = self._format_time(elapsed_seconds)
            self.elapsed_time_label.configure(text=elapsed_str)

            # 计算预计剩余时间
            progress = self.simulation_progress['value']
            if progress > 0:
                total_estimated = elapsed_seconds * 100 / progress
                remaining_seconds = total_estimated - elapsed_seconds
                if remaining_seconds > 0:
                    eta_str = self._format_time(remaining_seconds)
                    self.eta_label.configure(text=eta_str)
                else:
                    self.eta_label.configure(text="即将完成")
            else:
                self.eta_label.configure(text="计算中...")

        # 每秒更新一次
        if self.simulation_running:
            self.root.after(1000, self._update_time_display)

    def _format_time(self, seconds):
        """格式化时间显示"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        seconds = int(seconds % 60)
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"

    def _simulation_completed(self):
        """Handle simulation completion."""
        # Reset simulation control variables
        self.simulation_running = False
        self.simulation_paused = False
        self.simulation_stopped = False

        # Update UI
        self.simulation_progress['value'] = 100
        self.sim_status_label.configure(text="已完成")
        self.start_simulation_button.configure(state=tk.NORMAL)
        self.pause_simulation_button.configure(state=tk.DISABLED, text="暂停模拟")
        self.stop_simulation_button.configure(state=tk.DISABLED)

        # 更新最终时间显示
        self.eta_label.configure(text="00:00:00")

        # 生成更真实的模拟结果
        import random
        total_patients = int(self.num_patients_var.get())

        # 基于患者数量生成合理的结果
        cancer_cases = int(total_patients * random.uniform(0.01, 0.03))  # 1-3%癌症发病率
        cancer_deaths = int(cancer_cases * random.uniform(0.3, 0.5))     # 30-50%死亡率
        adenomas_detected = int(total_patients * random.uniform(0.15, 0.25))  # 15-25%腺瘤检出率
        advanced_adenomas = int(adenomas_detected * random.uniform(0.1, 0.2))  # 10-20%进展期腺瘤

        # 计算总运行时间
        import time
        if hasattr(self, 'simulation_start_time'):
            total_time = time.time() - self.simulation_start_time
            time_str = self._format_time(total_time)
        else:
            time_str = "未知"

        # Display results
        results_content = "模拟完成！\n\n"
        results_content += "=== 主要结果 ===\n"
        results_content += f"- 总患者数: {total_patients:,}\n"
        results_content += f"- 癌症病例: {cancer_cases:,}\n"
        results_content += f"- 癌症死亡: {cancer_deaths:,}\n"
        results_content += f"- 检出腺瘤: {adenomas_detected:,}\n"
        results_content += f"- 进展期腺瘤: {advanced_adenomas:,}\n"
        results_content += f"- 总运行时间: {time_str}\n\n"

        results_content += "=== 统计指标 ===\n"
        results_content += f"- 癌症发病率: {cancer_cases/total_patients*100:.2f}%\n"
        results_content += f"- 癌症死亡率: {cancer_deaths/total_patients*100:.2f}%\n"
        results_content += f"- 腺瘤检出率: {adenomas_detected/total_patients*100:.2f}%\n"
        results_content += f"- 进展期腺瘤比例: {advanced_adenomas/adenomas_detected*100:.2f}%\n"

        self.results_text.delete(1.0, tk.END)
        self.results_text.insert(tk.END, results_content)

        self.set_status("模拟完成")
        messagebox.showinfo("模拟完成", f"仿真模拟已成功完成！\n总运行时间: {time_str}")

    def _simulation_error(self, error_message: str):
        """Handle simulation error."""
        # Reset simulation control variables
        self.simulation_running = False
        self.simulation_paused = False
        self.simulation_stopped = False

        # Update UI
        self.sim_status_label.configure(text="错误")
        self.start_simulation_button.configure(state=tk.NORMAL)
        self.pause_simulation_button.configure(state=tk.DISABLED, text="暂停模拟")
        self.stop_simulation_button.configure(state=tk.DISABLED)

        self.set_status("模拟失败")
        messagebox.showerror("模拟错误", f"模拟过程中发生错误:\n{error_message}")

    def set_status(self, message: str):
        """Set status bar message."""
        self.status_label.configure(text=message)

    def _on_population_type_change(self, event=None):
        """处理人口类型变化"""
        population_type = self.population_type_var.get()
        if population_type == "birth_cohort":
            # 出生队列模式，建议使用参数文件
            self.use_default_population_var.set(False)
            self._toggle_population_file()

    def _toggle_population_file(self):
        """切换人口参数文件输入状态"""
        if self.use_default_population_var.get():
            # 使用默认参数，禁用文件选择
            self.population_file_var.set("")
            # 这里可以添加禁用文件选择控件的代码
        else:
            # 启用文件选择
            pass

    def browse_population_file(self):
        """浏览人口参数文件"""
        file_path = filedialog.askopenfilename(
            title="选择人口参数文件",
            filetypes=[
                ("Excel文件", "*.xlsx *.xls"),
                ("JSON文件", "*.json"),
                ("所有文件", "*.*")
            ]
        )

        if file_path:
            self.population_file_var.set(file_path)
            self.use_default_population_var.set(False)

            # 尝试加载人口参数文件
            try:
                success = self.settings.load_population_file(file_path)
                if success:
                    self.set_status(f"成功加载人口参数文件: {os.path.basename(file_path)}")
                    messagebox.showinfo("加载成功", f"人口参数文件加载成功:\n{os.path.basename(file_path)}")
                else:
                    self.set_status("人口参数文件加载失败")
                    messagebox.showerror("加载失败", "人口参数文件格式不正确或包含无效数据")
            except Exception as e:
                self.set_status("人口参数文件加载出错")
                messagebox.showerror("加载错误", f"加载人口参数文件时发生错误:\n{str(e)}")

    def create_population_template(self):
        """创建人口参数模板文件"""
        # 选择保存位置和格式
        file_path = filedialog.asksaveasfilename(
            title="保存人口参数模板",
            defaultextension=".xlsx",
            filetypes=[
                ("Excel文件", "*.xlsx"),
                ("JSON文件", "*.json")
            ]
        )

        if file_path:
            try:
                # 确定文件格式
                file_ext = os.path.splitext(file_path)[1].lower()
                format_type = "excel" if file_ext in ['.xlsx', '.xls'] else "json"

                # 创建模板
                success = self.settings.create_population_template(file_path, format_type)

                if success:
                    self.set_status(f"人口参数模板已创建: {os.path.basename(file_path)}")
                    messagebox.showinfo(
                        "模板创建成功",
                        f"人口参数模板已创建:\n{file_path}\n\n"
                        f"请根据需要修改模板中的参数值，然后重新加载。"
                    )
                else:
                    self.set_status("人口参数模板创建失败")
                    messagebox.showerror("创建失败", "无法创建人口参数模板文件")

            except Exception as e:
                self.set_status("人口参数模板创建出错")
                messagebox.showerror("创建错误", f"创建人口参数模板时发生错误:\n{str(e)}")

    def generate_convergence_plot(self):
        """生成收敛图"""
        try:
            if not self.calibration_results:
                messagebox.showwarning("警告", "没有可用的调参结果")
                return

            from ..calibration.visualization import calibration_visualizer

            # 获取调参历史
            history = self.calibration_results.get('history', [])
            if not history:
                messagebox.showwarning("警告", "没有调参历史数据")
                return

            # 生成收敛图
            plot_path = calibration_visualizer.plot_convergence_history(history)

            if plot_path:
                self.viz_text.delete(1.0, tk.END)
                self.viz_text.insert(tk.END, f"收敛图已生成: {plot_path}\n\n")
                self.viz_text.insert(tk.END, "图表包含以下内容:\n")
                self.viz_text.insert(tk.END, "- 误差收敛曲线\n")
                self.viz_text.insert(tk.END, "- 参数变化曲线\n")
                self.viz_text.insert(tk.END, "- 误差分布直方图\n")
                self.viz_text.insert(tk.END, "- 收敛速度分析\n")

                # 询问是否打开图片
                if messagebox.askyesno("打开图片", "是否打开生成的收敛图？"):
                    import subprocess
                    import platform

                    if platform.system() == "Windows":
                        os.startfile(plot_path)
                    elif platform.system() == "Darwin":  # macOS
                        subprocess.run(["open", plot_path])
                    else:  # Linux
                        subprocess.run(["xdg-open", plot_path])
            else:
                messagebox.showerror("错误", "生成收敛图失败")

        except Exception as e:
            messagebox.showerror("错误", f"生成收敛图时发生错误:\n{str(e)}")

    def generate_parameter_plot(self):
        """生成参数对比图"""
        try:
            if not self.calibration_results:
                messagebox.showwarning("警告", "没有可用的调参结果")
                return

            from ..calibration.visualization import calibration_visualizer

            # 生成参数对比图
            plot_path = calibration_visualizer.plot_parameter_comparison(self.calibration_results)

            if plot_path:
                self.viz_text.delete(1.0, tk.END)
                self.viz_text.insert(tk.END, f"参数对比图已生成: {plot_path}\n\n")
                self.viz_text.insert(tk.END, "图表包含以下内容:\n")
                self.viz_text.insert(tk.END, "- 最优参数值柱状图\n")
                self.viz_text.insert(tk.END, "- 参数相对重要性饼图\n")

                # 询问是否打开图片
                if messagebox.askyesno("打开图片", "是否打开生成的参数对比图？"):
                    import subprocess
                    import platform

                    if platform.system() == "Windows":
                        os.startfile(plot_path)
                    elif platform.system() == "Darwin":  # macOS
                        subprocess.run(["open", plot_path])
                    else:  # Linux
                        subprocess.run(["xdg-open", plot_path])
            else:
                messagebox.showerror("错误", "生成参数对比图失败")

        except Exception as e:
            messagebox.showerror("错误", f"生成参数对比图时发生错误:\n{str(e)}")

    def generate_calibration_comparison_plot(self):
        """生成调参对比图：基准值 vs 校准后模型计算值"""
        try:
            if not self.calibration_results:
                messagebox.showwarning("警告", "没有可用的调参结果")
                return

            from ..calibration.visualization import calibration_visualizer

            # 获取基准值数据
            benchmarks = self._load_benchmark_data()

            # 生成调参对比图
            plot_path = calibration_visualizer.plot_calibration_results_comparison(
                self.calibration_results, benchmarks
            )

            if plot_path:
                self.viz_text.delete(1.0, tk.END)
                self.viz_text.insert(tk.END, f"调参对比图已生成: {plot_path}\n\n")
                self.viz_text.insert(tk.END, "图表包含以下内容:\n")
                self.viz_text.insert(tk.END, "- 点线图显示不同年龄的腺瘤患病率趋势\n")
                self.viz_text.insert(tk.END, "- 癌症发病率的基准值 vs 校准后模型值对比\n")
                self.viz_text.insert(tk.END, "- 阴影区域显示校准后模型值的95%置信区间\n")
                self.viz_text.insert(tk.END, "- 按性别分组的详细趋势分析\n")
                self.viz_text.insert(tk.END, "- 数值标签显示具体数值和置信区间范围\n")

                # 询问是否打开图片
                if messagebox.askyesno("打开图片", "是否打开生成的调参对比图？"):
                    import subprocess
                    import platform

                    if platform.system() == "Windows":
                        os.startfile(plot_path)
                    elif platform.system() == "Darwin":  # macOS
                        subprocess.run(["open", plot_path])
                    else:  # Linux
                        subprocess.run(["xdg-open", plot_path])
            else:
                messagebox.showerror("错误", "生成调参对比图失败")

        except Exception as e:
            messagebox.showerror("错误", f"生成调参对比图时发生错误:\n{str(e)}")

    def generate_calibration_report(self):
        """生成完整的调参报告"""
        try:
            if not self.calibration_results:
                messagebox.showwarning("警告", "没有可用的调参结果")
                return

            from ..calibration.visualization import calibration_visualizer

            # 获取基准值数据
            benchmarks = self._load_benchmark_data()

            # 先生成调参对比图
            comparison_plot = calibration_visualizer.plot_calibration_results_comparison(
                self.calibration_results, benchmarks
            )

            # 生成完整报告
            history = self.calibration_results.get('history', [])
            report_path = calibration_visualizer.create_calibration_report(
                self.calibration_results,
                history
            )

            if report_path:
                self.viz_text.delete(1.0, tk.END)
                self.viz_text.insert(tk.END, f"调参报告已生成: {report_path}\n\n")
                self.viz_text.insert(tk.END, "报告包含以下内容:\n")
                self.viz_text.insert(tk.END, "- 调参概要信息\n")
                self.viz_text.insert(tk.END, "- 最优参数表格\n")
                self.viz_text.insert(tk.END, "- 收敛历史图表\n")
                self.viz_text.insert(tk.END, "- 参数分析图表\n")
                self.viz_text.insert(tk.END, "- 基准值vs校准后模型值对比图\n")

                if comparison_plot:
                    self.viz_text.insert(tk.END, f"- 调参对比图: {comparison_plot}\n")

                # 询问是否打开报告
                if messagebox.askyesno("打开报告", "是否打开生成的HTML报告？"):
                    import webbrowser
                    webbrowser.open(f"file://{os.path.abspath(report_path)}")
            else:
                messagebox.showerror("错误", "生成调参报告失败")

        except Exception as e:
            messagebox.showerror("错误", f"生成调参报告时发生错误:\n{str(e)}")

    def refresh_calibration_log(self):
        """刷新调参日志"""
        try:
            from ..calibration.logging_manager import calibration_logger

            # 获取当前会话摘要
            summary = calibration_logger.get_session_summary()

            self.log_text.delete(1.0, tk.END)

            if summary.get('status') == 'no_iterations':
                self.log_text.insert(tk.END, "当前没有活动的调参会话\n")
            else:
                self.log_text.insert(tk.END, "=== 调参会话摘要 ===\n")
                self.log_text.insert(tk.END, f"调参方法: {summary.get('method', 'N/A')}\n")
                self.log_text.insert(tk.END, f"总迭代次数: {summary.get('total_iterations', 0)}\n")
                self.log_text.insert(tk.END, f"开始时间: {summary.get('start_time', 'N/A')}\n")
                self.log_text.insert(tk.END, f"初始误差: {summary.get('initial_error', 'N/A')}\n")
                self.log_text.insert(tk.END, f"最终误差: {summary.get('final_error', 'N/A')}\n")
                self.log_text.insert(tk.END, f"最佳误差: {summary.get('best_error', 'N/A')}\n")
                self.log_text.insert(tk.END, f"误差改善: {summary.get('error_improvement', 0):.6f}\n")
                self.log_text.insert(tk.END, f"是否收敛: {'是' if summary.get('convergence_achieved') else '否'}\n")

        except Exception as e:
            self.log_text.delete(1.0, tk.END)
            self.log_text.insert(tk.END, f"刷新日志时发生错误: {str(e)}\n")

    def export_calibration_log(self):
        """导出调参日志"""
        try:
            from ..calibration.logging_manager import calibration_logger

            # 选择导出路径
            file_path = filedialog.asksaveasfilename(
                title="导出调参日志",
                defaultextension=".json",
                filetypes=[
                    ("JSON文件", "*.json"),
                    ("所有文件", "*.*")
                ]
            )

            if file_path:
                export_path = calibration_logger.export_history(file_path)
                messagebox.showinfo("导出成功", f"调参日志已导出到:\n{export_path}")

        except Exception as e:
            messagebox.showerror("导出错误", f"导出调参日志时发生错误:\n{str(e)}")

    def clear_calibration_log(self):
        """清空调参日志显示"""
        if messagebox.askyesno("确认清空", "是否清空当前显示的日志内容？\n（这不会删除实际的日志文件）"):
            self.log_text.delete(1.0, tk.END)
            self.log_text.insert(tk.END, "日志显示已清空\n")

"""
调参结果可视化模块

该模块提供调参过程和结果的可视化功能，包括参数收敛图、误差变化图、参数分布图等。
"""

import os
import json
import logging
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

logger = logging.getLogger(__name__)


class CalibrationVisualizer:
    """调参结果可视化器"""
    
    def __init__(self, output_dir: str = "calibration_results"):
        """
        初始化可视化器
        
        Args:
            output_dir: 输出目录
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建图表子目录
        self.plots_dir = self.output_dir / "plots"
        self.plots_dir.mkdir(exist_ok=True)
        
    def plot_convergence_history(self, 
                                history: List[Dict[str, Any]], 
                                save_path: Optional[str] = None) -> str:
        """
        绘制参数收敛历史图
        
        Args:
            history: 调参历史记录
            save_path: 保存路径
            
        Returns:
            str: 图片保存路径
        """
        if not history:
            logger.warning("调参历史记录为空，无法绘制收敛图")
            return ""
        
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        fig.suptitle('调参收敛历史', fontsize=16, fontweight='bold')
        
        # 提取数据
        iterations = list(range(len(history)))
        errors = [h.get('error', 0) for h in history]
        
        # 误差收敛图
        axes[0, 0].plot(iterations, errors, 'b-', linewidth=2, marker='o', markersize=4)
        axes[0, 0].set_title('误差收敛曲线')
        axes[0, 0].set_xlabel('迭代次数')
        axes[0, 0].set_ylabel('误差值')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 参数变化图
        param_names = []
        param_values = {}
        
        for h in history:
            params = h.get('parameters', {})
            for name, value in params.items():
                if name not in param_values:
                    param_values[name] = []
                    param_names.append(name)
                param_values[name].append(value)
        
        # 绘制前3个参数的变化
        colors = ['red', 'green', 'blue']
        for i, param_name in enumerate(param_names[:3]):
            if i < len(colors):
                axes[0, 1].plot(iterations, param_values[param_name], 
                              color=colors[i], linewidth=2, marker='s', 
                              markersize=3, label=param_name)
        
        axes[0, 1].set_title('参数变化曲线')
        axes[0, 1].set_xlabel('迭代次数')
        axes[0, 1].set_ylabel('参数值')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # 误差分布直方图
        axes[1, 0].hist(errors, bins=min(20, len(errors)), alpha=0.7, color='skyblue', edgecolor='black')
        axes[1, 0].set_title('误差分布直方图')
        axes[1, 0].set_xlabel('误差值')
        axes[1, 0].set_ylabel('频次')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 收敛速度分析
        if len(errors) > 1:
            error_diff = np.diff(errors)
            axes[1, 1].plot(iterations[1:], error_diff, 'purple', linewidth=2, marker='^', markersize=3)
            axes[1, 1].set_title('误差变化率')
            axes[1, 1].set_xlabel('迭代次数')
            axes[1, 1].set_ylabel('误差变化量')
            axes[1, 1].grid(True, alpha=0.3)
            axes[1, 1].axhline(y=0, color='red', linestyle='--', alpha=0.5)
        
        plt.tight_layout()
        
        # 保存图片
        if save_path is None:
            save_path = self.plots_dir / "convergence_history.png"
        
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"收敛历史图已保存: {save_path}")
        return str(save_path)
    
    def plot_calibration_results_comparison(self,
                                           results: Dict[str, Any],
                                           benchmarks: Dict[str, Any],
                                           save_path: Optional[str] = None) -> str:
        """
        绘制调参结果对比图：基准值 vs 校准后模型计算值

        Args:
            results: 调参结果
            benchmarks: 基准值数据
            save_path: 保存路径

        Returns:
            str: 图片保存路径
        """
        if not results or not benchmarks:
            logger.warning("缺少调参结果或基准值数据")
            return ""

        # 创建子图
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('基准值 vs 校准值',
                    fontsize=16, fontweight='bold', y=0.95)

        # 定义年龄组和性别 - 确保包含所有基准数据点
        age_groups = [20, 30, 40, 50, 60, 70, 80, 90, 100]
        genders = ['M', 'F']
        gender_labels = {'M': '男性', 'F': '女性'}

        # 定义疾病类型
        diseases = ['early_adenoma_prevalence', 'advanced_adenoma_prevalence', 'cancer_incidence']
        disease_labels = {
            'early_adenoma_prevalence': '早期腺瘤患病率',
            'advanced_adenoma_prevalence': '进展期腺瘤患病率',
            'cancer_incidence': '癌症发病率'
        }

        # 为每种疾病创建对比图
        for i, disease in enumerate(diseases):
            for j, gender in enumerate(genders):
                ax = axes[j, i]

                # 提取基准值数据
                benchmark_data = benchmarks.get(disease, {}).get(gender, {})

                # 模拟校准后的模型计算值（实际应该从调参结果中获取）
                model_data = self._simulate_model_results(benchmark_data, results)

                # 准备绘图数据
                ages = []
                benchmark_values = []
                model_values = []
                model_ci_lower = []
                model_ci_upper = []

                # 获取所有可用的年龄数据点
                available_ages = sorted(benchmark_data.keys()) if benchmark_data else []

                # 如果基准数据为空，使用默认年龄组
                if not available_ages:
                    available_ages = [50, 60, 70]  # 默认显示的年龄组

                for age in available_ages:
                    ages.append(age)
                    benchmark_values.append(benchmark_data[age])

                    # 模型计算值和置信区间
                    model_val = model_data.get(age, {})
                    model_values.append(model_val.get('value', benchmark_data[age]))
                    model_ci_lower.append(model_val.get('ci_lower', benchmark_data[age] * 0.9))
                    model_ci_upper.append(model_val.get('ci_upper', benchmark_data[age] * 1.1))

                if ages:
                    # 绘制基准值点线图
                    ax.plot(ages, benchmark_values, 'o-',
                           label='基准值', color='blue', linewidth=2,
                           markersize=8, markerfacecolor='lightblue',
                           markeredgecolor='blue', markeredgewidth=2)

                    # 绘制模型计算值点线图
                    ax.plot(ages, model_values, 's-',
                           label='校准后模型值', color='red', linewidth=2,
                           markersize=8, markerfacecolor='lightcoral',
                           markeredgecolor='red', markeredgewidth=2)

                    # 添加95% CI置信区间阴影
                    ax.fill_between(ages, model_ci_lower, model_ci_upper,
                                   color='red', alpha=0.2, label='95% 置信区间')

                    # 设置图表属性
                    ax.set_title(f'{disease_labels[disease]} - {gender_labels[gender]}',
                               fontsize=12, fontweight='bold')
                    ax.set_xlabel('年龄', fontsize=10)
                    ax.set_ylabel('率值 (每10万人)', fontsize=10)

                    # 设置x轴显示完整的年龄范围（20-100岁，每10岁一个刻度）
                    full_age_range = list(range(20, 101, 10))
                    ax.set_xlim(15, 105)  # 稍微扩展范围以便更好显示
                    ax.set_xticks(full_age_range)
                    ax.set_xticklabels([str(age) for age in full_age_range])

                    ax.legend(fontsize=9, loc='best')
                    ax.grid(True, alpha=0.3, linestyle='--')

                    # 设置坐标轴范围，确保所有数据可见
                    all_values = benchmark_values + model_values + model_ci_lower + model_ci_upper
                    y_min = min(all_values) * 0.9
                    y_max = max(all_values) * 1.1
                    ax.set_ylim(y_min, y_max)

                    # 添加数值标签
                    for x, y in zip(ages, benchmark_values):
                        ax.annotate(f'{y:.1f}', (x, y), textcoords="offset points",
                                  xytext=(0,10), ha='center', fontsize=8, color='blue')

                    for x, y in zip(ages, model_values):
                        ax.annotate(f'{y:.1f}', (x, y), textcoords="offset points",
                                  xytext=(0,-15), ha='center', fontsize=8, color='red')

                    # 添加置信区间数值标签（可选）
                    for x, lower, upper in zip(ages, model_ci_lower, model_ci_upper):
                        ax.annotate(f'[{lower:.1f}, {upper:.1f}]', (x, upper),
                                  textcoords="offset points", xytext=(0,5),
                                  ha='center', fontsize=7, color='gray', alpha=0.8)

        plt.tight_layout()

        # 保存图片
        if save_path is None:
            save_path = self.plots_dir / "calibration_comparison.png"

        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

        logger.info(f"调参对比图已保存: {save_path}")
        return str(save_path)

    def _simulate_model_results(self, benchmark_data: Dict[int, float],
                               results: Dict[str, Any]) -> Dict[int, Dict[str, float]]:
        """
        模拟校准后的模型计算结果

        Args:
            benchmark_data: 基准值数据
            results: 调参结果

        Returns:
            Dict: 模型计算结果，包含值和置信区间
        """
        model_results = {}

        # 从调参结果中获取校准因子
        calibration_factor = 1.0
        if 'parameters' in results:
            # 这里应该根据实际的调参参数计算校准因子
            # 现在使用简化的计算方式
            params = results['parameters']
            if 'early_adenoma_rate' in params:
                calibration_factor = params['early_adenoma_rate'] / 0.025  # 假设基准值

        for age, benchmark_value in benchmark_data.items():
            # 应用校准因子
            calibrated_value = benchmark_value * calibration_factor

            # 计算95%置信区间（基于调参误差）
            error = results.get('error', 0.1)
            ci_range = calibrated_value * error

            model_results[age] = {
                'value': calibrated_value,
                'ci_lower': max(0, calibrated_value - ci_range),
                'ci_upper': calibrated_value + ci_range
            }

        return model_results

    def plot_parameter_comparison(self,
                                 results: Dict[str, Any],
                                 save_path: Optional[str] = None) -> str:
        """
        绘制参数对比图

        Args:
            results: 调参结果
            save_path: 保存路径

        Returns:
            str: 图片保存路径
        """
        parameters = results.get('parameters', {})
        if not parameters:
            logger.warning("调参结果中没有参数信息")
            return ""

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))
        fig.suptitle('调参结果参数分析', fontsize=16, fontweight='bold')

        param_names = list(parameters.keys())
        param_values = list(parameters.values())

        # 参数值柱状图
        bars = ax1.bar(range(len(param_names)), param_values,
                      color=['skyblue', 'lightgreen', 'lightcoral', 'gold', 'plum'][:len(param_names)])
        ax1.set_title('最优参数值')
        ax1.set_xlabel('参数名称')
        ax1.set_ylabel('参数值')
        ax1.set_xticks(range(len(param_names)))
        ax1.set_xticklabels(param_names, rotation=45, ha='right')
        ax1.grid(True, alpha=0.3)

        # 在柱状图上添加数值标签
        for bar, value in zip(bars, param_values):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height,
                    f'{value:.4f}', ha='center', va='bottom')

        # 参数重要性分析（基于值的大小）
        normalized_values = np.array(param_values) / np.max(np.abs(param_values))
        importance = np.abs(normalized_values)

        wedges, texts, autotexts = ax2.pie(importance, labels=param_names, autopct='%1.1f%%',
                                          colors=['skyblue', 'lightgreen', 'lightcoral', 'gold', 'plum'][:len(param_names)])
        ax2.set_title('参数相对重要性')

        plt.tight_layout()

        # 保存图片
        if save_path is None:
            save_path = self.plots_dir / "parameter_comparison.png"

        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

        logger.info(f"参数对比图已保存: {save_path}")
        return str(save_path)
    
    def plot_method_comparison(self, 
                              method_results: Dict[str, Dict[str, Any]], 
                              save_path: Optional[str] = None) -> str:
        """
        绘制不同调参方法的对比图
        
        Args:
            method_results: 不同方法的结果
            save_path: 保存路径
            
        Returns:
            str: 图片保存路径
        """
        if not method_results:
            logger.warning("没有方法结果可供对比")
            return ""
        
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        fig.suptitle('调参方法对比分析', fontsize=16, fontweight='bold')
        
        methods = list(method_results.keys())
        errors = [method_results[m].get('error', 0) for m in methods]
        times = [method_results[m].get('execution_time', 0) for m in methods]
        
        # 误差对比
        bars1 = axes[0, 0].bar(methods, errors, color='lightcoral', alpha=0.7)
        axes[0, 0].set_title('方法误差对比')
        axes[0, 0].set_ylabel('误差值')
        axes[0, 0].tick_params(axis='x', rotation=45)
        axes[0, 0].grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar, error in zip(bars1, errors):
            height = bar.get_height()
            axes[0, 0].text(bar.get_x() + bar.get_width()/2., height,
                           f'{error:.4f}', ha='center', va='bottom')
        
        # 执行时间对比
        bars2 = axes[0, 1].bar(methods, times, color='lightblue', alpha=0.7)
        axes[0, 1].set_title('执行时间对比')
        axes[0, 1].set_ylabel('时间 (秒)')
        axes[0, 1].tick_params(axis='x', rotation=45)
        axes[0, 1].grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar, time in zip(bars2, times):
            height = bar.get_height()
            axes[0, 1].text(bar.get_x() + bar.get_width()/2., height,
                           f'{time:.1f}s', ha='center', va='bottom')
        
        # 效率分析（误差/时间）
        efficiency = [e/t if t > 0 else 0 for e, t in zip(errors, times)]
        bars3 = axes[1, 0].bar(methods, efficiency, color='lightgreen', alpha=0.7)
        axes[1, 0].set_title('效率分析 (误差/时间)')
        axes[1, 0].set_ylabel('效率值')
        axes[1, 0].tick_params(axis='x', rotation=45)
        axes[1, 0].grid(True, alpha=0.3)
        
        # 综合评分（基于误差和时间的加权）
        max_error = max(errors) if errors else 1
        max_time = max(times) if times else 1
        scores = [(1 - e/max_error) * 0.7 + (1 - t/max_time) * 0.3 for e, t in zip(errors, times)]
        
        bars4 = axes[1, 1].bar(methods, scores, color='gold', alpha=0.7)
        axes[1, 1].set_title('综合评分')
        axes[1, 1].set_ylabel('评分 (0-1)')
        axes[1, 1].tick_params(axis='x', rotation=45)
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图片
        if save_path is None:
            save_path = self.plots_dir / "method_comparison.png"
        
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"方法对比图已保存: {save_path}")
        return str(save_path)
    
    def create_calibration_report(self, 
                                 results: Dict[str, Any], 
                                 history: Optional[List[Dict[str, Any]]] = None,
                                 method_results: Optional[Dict[str, Dict[str, Any]]] = None) -> str:
        """
        创建完整的调参报告
        
        Args:
            results: 调参结果
            history: 调参历史
            method_results: 不同方法的结果
            
        Returns:
            str: 报告文件路径
        """
        report_path = self.output_dir / "calibration_report.html"
        
        # 生成图表
        plots = {}
        if history:
            plots['convergence'] = self.plot_convergence_history(history)
        
        if results:
            plots['parameters'] = self.plot_parameter_comparison(results)
        
        if method_results:
            plots['methods'] = self.plot_method_comparison(method_results)
        
        # 生成HTML报告
        html_content = self._generate_html_report(results, plots)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.info(f"调参报告已生成: {report_path}")
        return str(report_path)
    
    def _generate_html_report(self, results: Dict[str, Any], plots: Dict[str, str]) -> str:
        """生成HTML格式的调参报告"""
        html = f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>CMOST调参结果报告</title>
            <style>
                body {{ font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; }}
                .header {{ text-align: center; color: #2c3e50; }}
                .section {{ margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }}
                .parameter {{ background-color: #f8f9fa; padding: 10px; margin: 5px 0; border-radius: 3px; }}
                .plot {{ text-align: center; margin: 20px 0; }}
                .plot img {{ max-width: 100%; height: auto; border: 1px solid #ddd; }}
                table {{ width: 100%; border-collapse: collapse; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>CMOST调参结果报告</h1>
                <p>生成时间: {np.datetime64('now')}</p>
            </div>
            
            <div class="section">
                <h2>调参概要</h2>
                <div class="parameter">
                    <strong>调参方法:</strong> {results.get('method', 'N/A')}
                </div>
                <div class="parameter">
                    <strong>最终误差:</strong> {results.get('error', 'N/A'):.6f}
                </div>
                <div class="parameter">
                    <strong>执行时间:</strong> {results.get('execution_time', 'N/A'):.2f} 秒
                </div>
                <div class="parameter">
                    <strong>收敛状态:</strong> {results.get('convergence_info', {}).get('converged', 'N/A')}
                </div>
            </div>
            
            <div class="section">
                <h2>最优参数</h2>
                <table>
                    <tr><th>参数名称</th><th>参数值</th></tr>
        """
        
        # 添加参数表格
        parameters = results.get('parameters', {})
        for name, value in parameters.items():
            html += f"<tr><td>{name}</td><td>{value:.6f}</td></tr>"
        
        html += """
                </table>
            </div>
        """
        
        # 添加图表
        for plot_type, plot_path in plots.items():
            if plot_path and os.path.exists(plot_path):
                plot_name = {
                    'convergence': '收敛历史',
                    'parameters': '参数分析',
                    'methods': '方法对比'
                }.get(plot_type, plot_type)
                
                # 使用相对路径
                rel_path = os.path.relpath(plot_path, self.output_dir)
                html += f"""
            <div class="section">
                <h2>{plot_name}</h2>
                <div class="plot">
                    <img src="{rel_path}" alt="{plot_name}">
                </div>
            </div>
                """
        
        html += """
        </body>
        </html>
        """
        
        return html


# 全局可视化器实例
calibration_visualizer = CalibrationVisualizer()
